# C/C++ build system timings
generate_cxx_metadata
  [gap of 53ms]
  create-invalidation-state 71ms
  generate-prefab-packages
    [gap of 59ms]
    exec-prefab 1193ms
    [gap of 53ms]
  generate-prefab-packages completed in 1305ms
  execute-generate-process
    exec-configure 515ms
    [gap of 76ms]
  execute-generate-process completed in 591ms
  [gap of 77ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 2117ms

