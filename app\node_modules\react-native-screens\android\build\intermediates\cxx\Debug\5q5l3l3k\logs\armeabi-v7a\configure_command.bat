@echo off
"C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HK:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Android\\android-sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Android\\android-sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\5q5l3l3k\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\5q5l3l3k\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\armeabi-v7a\\prefab" ^
  "-BK:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\armeabi-v7a" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DRNS_NEW_ARCH_ENABLED=true" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
