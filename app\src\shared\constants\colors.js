/**
 * Centralized Color Constants
 * All colors used throughout the app should be defined here
 * This makes it easy to change colors globally by updating them in one place
 */

// Miles-Inspired Brand Colors
export const BRAND_COLORS = {
  // Primary brand colors
  EXPLORER_TEAL: "#5BC4B3",
  SKY_AQUA: "#A3E8DC",
  OCEAN_BLUE: "#36798A",
  RUCKSACK_BROWN: "#A46E3E",
  SHADOW_GREY: "#4F4F4F",
  WHISPER_WHITE: "#FAFAFA",

  // Card background color - easy to change in one place
  CARD_BACKGROUND: "#ff5c57", // Coral Red - your requested color

  // Divider/separator color - your requested coral red
  DIVIDER_COLOR: "#ff5c57", // Coral Red - your requested color
};

// Neutral Colors
export const NEUTRAL_COLORS = {
  50: "#FAFAFA",
  100: "#F8F9FA",
  200: "#E9ECEF",
  300: "#DEE2E6",
  400: "#CED4DA",
  500: "#ADB5BD",
  600: "#6C757D",
  700: "#495057",
  800: "#343A40",
  900: "#212529",
};

// Semantic Colors
export const SEMANTIC_COLORS = {
  SUCCESS: BRAND_COLORS.EXPLORER_TEAL,
  WARNING: BRAND_COLORS.RUCKSACK_BROWN,
  ERROR: "#FF6B6B",
  INFO: BRAND_COLORS.OCEAN_BLUE,
};

// Background Colors
export const BACKGROUND_COLORS = {
  DEFAULT: BRAND_COLORS.WHISPER_WHITE,
  PAPER: BRAND_COLORS.WHISPER_WHITE,
  CARD: BRAND_COLORS.CARD_BACKGROUND,
  DARK: BRAND_COLORS.OCEAN_BLUE,
};

// Text Colors
export const TEXT_COLORS = {
  PRIMARY: BRAND_COLORS.SHADOW_GREY,
  SECONDARY: BRAND_COLORS.OCEAN_BLUE,
  TERTIARY: BRAND_COLORS.RUCKSACK_BROWN,
  WHITE: BRAND_COLORS.WHISPER_WHITE,
  DISABLED: NEUTRAL_COLORS[400],
};

// Border Colors
export const BORDER_COLORS = {
  LIGHT: NEUTRAL_COLORS[200],
  MEDIUM: NEUTRAL_COLORS[300],
  DARK: BRAND_COLORS.SHADOW_GREY + "30",
  ACCENT: BRAND_COLORS.EXPLORER_TEAL + "40",
};

// Shadow Colors
export const SHADOW_COLORS = {
  DEFAULT: BRAND_COLORS.SHADOW_GREY,
  LIGHT: NEUTRAL_COLORS[300],
};
