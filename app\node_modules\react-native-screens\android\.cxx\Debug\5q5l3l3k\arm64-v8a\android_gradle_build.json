{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "rnscreens", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\5q5l3l3k\\obj\\arm64-v8a\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}