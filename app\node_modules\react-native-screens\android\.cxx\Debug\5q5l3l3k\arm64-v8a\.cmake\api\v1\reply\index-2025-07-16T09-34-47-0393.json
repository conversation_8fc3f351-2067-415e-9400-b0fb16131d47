{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Android/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Android/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Android/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "C:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-a6edd1c552e8d123012b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-733a8ffc9d25dc81da93.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-336fb40195da69dd7bd2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-733a8ffc9d25dc81da93.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-336fb40195da69dd7bd2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-a6edd1c552e8d123012b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}