/**
 * Modern Theme Configuration
 * A comprehensive theme with Miles-inspired colors, typography, and spacing
 * Based on UNextDoor brand guidelines and Miles mascot character
 *
 * Miles-Inspired Brand Colors:
 * - Explorer Teal: #5BC4B3 (Primary brand highlight, app accents, key buttons)
 * - Sky Aqua: #A3E8DC (Backgrounds, UI containers, hover effects)
 * - Ocean Blue: #36798A (Headings, text highlights, overlays)
 * - Canvas Beige: #F7E7C1 (Backgrounds, card elements, illustration base)
 * - Rucksack Brown: #A46E3E (Borders, shadows, accent details)
 * - Shadow Grey: #4F4F4F (Text, outlines, icons)
 * - Whisper White: #FAFAFA (Background base, UI space, feed balance)
 * - Font: Montserrat (Light, Regular, Medium, SemiBold, Bold, ExtraBold)
 */

import { FONTS } from "../utils/fontUtils";
import { Dimensions } from "react-native";
import { getBreakpoints } from "../utils/webConfig";
import { responsiveTypography } from "../utils/responsiveFonts";

// Miles-Inspired Color Palette for UNextDoor
const colors = {
  // Primary colors - Explorer Teal (#5BC4B3)
  primary: {
    50: "#E8F8F6",
    100: "#D1F1ED",
    200: "#A3E3DB",
    300: "#75D5C9",
    400: "#47C7B7",
    500: "#5BC4B3", // Main primary color - Explorer Teal
    600: "#4A9D90",
    700: "#38766D",
    800: "#264E4A",
    900: "#132727",
  },

  // Secondary colors - Ocean Blue (#36798A)
  secondary: {
    50: "#E9F2F4",
    100: "#D3E5E9",
    200: "#A7CBD3",
    300: "#7BB1BD",
    400: "#4F97A7",
    500: "#36798A", // Main secondary color - Ocean Blue
    600: "#2B616E",
    700: "#204953",
    800: "#153137",
    900: "#0A181C",
  },

  // Tertiary colors - Shadow Grey (#4F4F4F)
  tertiary: {
    50: "#F7F7F7",
    100: "#EFEFEF",
    200: "#DFDFDF",
    300: "#CFCFCF",
    400: "#BFBFBF",
    500: "#4F4F4F", // Main tertiary color - Shadow Grey
    600: "#3F3F3F",
    700: "#2F2F2F",
    800: "#1F1F1F",
    900: "#0F0F0F",
  },

  // Accent colors - Sky Aqua (#A3E8DC)
  accent: {
    50: "#F4FDFC",
    100: "#E9FBF9",
    200: "#D3F7F3",
    300: "#BDF3ED",
    400: "#A7EFE7",
    500: "#A3E8DC", // Main accent color - Sky Aqua
    600: "#82BAB0",
    700: "#628B84",
    800: "#415D58",
    900: "#212E2C",
  },

  // Neutral colors - Light Sky Aqua and Whisper White
  neutral: {
    50: "#FAFAFA", // Whisper White
    100: "#F0FCFB", // Very Light Sky Aqua for cards
    200: "#E6F9F7",
    300: "#DCF6F3",
    400: "#D2F3EF",
    500: "#C8F0EB",
    600: "#BEEEE7",
    700: "#B4EBE3",
    800: "#AAE8DF",
    900: "#A0E5DB",
  },

  // Semantic colors
  success: {
    light: "#A3E8DC", // Sky Aqua for light success
    main: "#5BC4B3", // Explorer Teal for success
    dark: "#38766D",
  },

  warning: {
    light: "#F7E7C1", // Canvas Beige for light warning
    main: "#A46E3E", // Rucksack Brown for warning
    dark: "#836B39",
  },

  error: {
    light: "#FFB3B3",
    main: "#FF6B6B",
    dark: "#E55555",
  },

  info: {
    light: "#A3E8DC", // Sky Aqua for light info
    main: "#36798A", // Ocean Blue for info
    dark: "#204953",
  },

  // Text colors
  text: {
    primary: "#4F4F4F", // Shadow Grey for primary text
    secondary: "#36798A", // Ocean Blue for secondary text
    tertiary: "#A46E3E", // Rucksack Brown for tertiary text
    disabled: "#BFBFBF",
    hint: "#BFBFBF",
    white: "#FAFAFA", // Whisper White
  },

  // Background colors
  background: {
    default: "#FAFAFA", // Whisper White background
    paper: "#FAFAFA", // Whisper White
    card: "#F8F9FA", // Soft Light Grey for cards
    dark: "#36798A", // Ocean Blue for dark backgrounds
  },

  // Divider color
  divider: "#F0DBA8", // Light Canvas Beige for dividers

  // Miles-inspired brand colors
  explorerTeal: "#5BC4B3", // Primary brand highlight
  skyAqua: "#A3E8DC", // Backgrounds, UI containers
  oceanBlue: "#36798A", // Headings, text highlights
  softLightGrey: "#F8F9FA", // Soft light grey for card backgrounds
  rucksackBrown: "#A46E3E", // Borders, shadows, accents
  shadowGrey: "#4F4F4F", // Text, outlines, icons
  whisperWhite: "#FAFAFA", // Background base, UI space
};

// Typography scale with proper hierarchy - Using Montserrat from brand guidelines
const typography = {
  fontFamily: {
    primary: FONTS.MONTSERRAT_REGULAR,
    secondary: FONTS.MONTSERRAT_REGULAR,
    light: FONTS.MONTSERRAT_LIGHT,
    regular: FONTS.MONTSERRAT_REGULAR,
    medium: FONTS.MONTSERRAT_MEDIUM,
    semibold: FONTS.MONTSERRAT_SEMIBOLD,
    bold: FONTS.MONTSERRAT_BOLD,
    extrabold: FONTS.MONTSERRAT_EXTRABOLD,
    italic: FONTS.MONTSERRAT_ITALIC,
  },

  // Responsive font sizes that scale with device size
  fontSize: responsiveTypography.fontSize,

  fontWeight: {
    light: "300",
    regular: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    extrabold: "800",
    black: "900",
  },

  lineHeight: {
    xs: 1.2,
    sm: 1.4,
    md: 1.5,
    lg: 1.8,
    xl: 2,
  },

  letterSpacing: {
    tighter: -0.5,
    tight: -0.25,
    normal: 0,
    wide: 0.25,
    wider: 0.5,
  },
};

// Spacing scale for consistent layout
const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  "2xl": 48,
  "3xl": 64,
  "4xl": 80,
};

// Border radius scale
const borderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  "2xl": 24,
  full: 9999,
};

// Shadows for elevation - Using navy blue for branded shadows
const shadows = {
  none: "none",
  xs: {
    shadowColor: "#003366", // Navy blue shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: "#003366", // Navy blue shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: "#003366", // Navy blue shadow
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  lg: {
    shadowColor: "#003366", // Navy blue shadow
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 4,
  },
  xl: {
    shadowColor: "#003366", // Navy blue shadow
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 5,
  },
  "2xl": {
    shadowColor: "#003366", // Navy blue shadow
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.2,
    shadowRadius: 24,
    elevation: 6,
  },
};

// Animation timing
const animation = {
  fast: "150ms",
  normal: "300ms",
  slow: "500ms",
  timing: "cubic-bezier(0.4, 0, 0.2, 1)",
};

// Z-index scale
const zIndex = {
  hide: -1,
  base: 0,
  raised: 1,
  dropdown: 1000,
  sticky: 1100,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  toast: 1700,
  tooltip: 1800,
};

// Screen dimensions - Web compatible
const getScreenDimensions = () => {
  try {
    const { width, height } = Dimensions.get("window");
    return {
      width,
      height,
      isSmall: width < 375,
      isMedium: width >= 375 && width < 768,
      isLarge: width >= 768,
      isTablet: width >= 768 && width < 1024,
      isDesktop: width >= 1024,
    };
  } catch (error) {
    // Fallback for web or when Dimensions is not available
    const width = typeof window !== "undefined" ? window.innerWidth : 375;
    const height = typeof window !== "undefined" ? window.innerHeight : 667;
    return {
      width,
      height,
      isSmall: width < 375,
      isMedium: width >= 375 && width < 768,
      isLarge: width >= 768,
      isTablet: width >= 768 && width < 1024,
      isDesktop: width >= 1024,
    };
  }
};

// Use web-compatible screen dimensions
const screen = (() => {
  try {
    return getScreenDimensions();
  } catch (error) {
    // Additional fallback using web config if available
    try {
      return getBreakpoints();
    } catch (webError) {
      // Final fallback
      return {
        width: 375,
        height: 667,
        isSmall: false,
        isMedium: true,
        isLarge: false,
        isTablet: false,
        isDesktop: false,
      };
    }
  }
})();

// Export the complete theme
const modernTheme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animation,
  zIndex,
  screen,
};

export default modernTheme;
