# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: GestureHandler
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/5u5x3i4z/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target gesturehandler


#############################################
# Order-only phony target for gesturehandler

build cmake_object_order_depends_target_gesturehandler: phony || CMakeFiles/gesturehandler.dir

build CMakeFiles/gesturehandler.dir/cpp-adapter.cpp.o: CXX_COMPILER__gesturehandler_Debug K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/src/main/jni/cpp-adapter.cpp || cmake_object_order_depends_target_gesturehandler
  DEFINES = -Dgesturehandler_EXPORTS
  DEP_FILE = CMakeFiles\gesturehandler.dir\cpp-adapter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IK:/2025/thenextdoor/app/node_modules/react-native/ReactCommon -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include
  OBJECT_DIR = CMakeFiles\gesturehandler.dir
  OBJECT_FILE_DIR = CMakeFiles\gesturehandler.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target gesturehandler


#############################################
# Link the shared library K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\5u5x3i4z\obj\armeabi-v7a\libgesturehandler.so

build K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/5u5x3i4z/obj/armeabi-v7a/libgesturehandler.so: CXX_SHARED_LIBRARY_LINKER__gesturehandler_Debug CMakeFiles/gesturehandler.dir/cpp-adapter.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\gesturehandler.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libgesturehandler.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\5u5x3i4z\obj\armeabi-v7a\libgesturehandler.so
  TARGET_PDB = gesturehandler.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\.cxx\Debug\5u5x3i4z\armeabi-v7a && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D K:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\.cxx\Debug\5u5x3i4z\armeabi-v7a && C:\Android\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SK:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\src\main\jni -BK:\2025\thenextdoor\app\node_modules\react-native-gesture-handler\android\.cxx\Debug\5u5x3i4z\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build gesturehandler: phony K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/5u5x3i4z/obj/armeabi-v7a/libgesturehandler.so

build libgesturehandler.so: phony K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/5u5x3i4z/obj/armeabi-v7a/libgesturehandler.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: K:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/5u5x3i4z/armeabi-v7a

build all: phony K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/5u5x3i4z/obj/armeabi-v7a/libgesturehandler.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/5u5x3i4z/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/5u5x3i4z/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Android/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Android/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/5u5x3i4z/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/5u5x3i4z/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake K$:/2025/thenextdoor/app/node_modules/react-native-gesture-handler/android/src/main/jni/CMakeLists.txt K$:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
