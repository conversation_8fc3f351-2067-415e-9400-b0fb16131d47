# C/C++ build system timings
generate_cxx_metadata
  [gap of 71ms]
  create-invalidation-state 154ms
  generate-prefab-packages
    exec-prefab 975ms
    [gap of 52ms]
  generate-prefab-packages completed in 1028ms
  execute-generate-process
    [gap of 12ms]
    exec-configure 507ms
    [gap of 66ms]
  execute-generate-process completed in 585ms
  [gap of 23ms]
  remove-unexpected-so-files 11ms
  [gap of 55ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 1946ms

