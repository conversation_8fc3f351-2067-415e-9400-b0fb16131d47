{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\5q5l3l3k\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "rnscreens", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\5q5l3l3k\\obj\\armeabi-v7a\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}