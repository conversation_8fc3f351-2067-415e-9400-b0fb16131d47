{"buildFiles": ["K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\5u5x3i4z\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"artifactName": "gesturehandler", "abi": "armeabi-v7a", "output": "K:\\2025\\thenextdoor\\app\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\5u5x3i4z\\obj\\armeabi-v7a\\libgesturehandler.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so"]}}}