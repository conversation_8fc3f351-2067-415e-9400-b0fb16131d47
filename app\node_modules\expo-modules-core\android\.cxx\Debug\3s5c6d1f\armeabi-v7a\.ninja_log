# ninja log v5
26	7062	7743616237889739	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	e11269260b82309c
212	7399	7743616241459855	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	e0718ba9ab763cb7
40	8186	7743616249096613	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o	91d952f5a09fb8d4
77	10889	7743616275705324	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	9ec67d0f5d40160
95	11578	7743616281463180	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	ee1cfd3dee90cad2
10	12176	7743616286320280	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o	b54bc8155e5e59c8
292	12858	7743616295451113	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	1bc59a6d79c06db7
1	14500	7743616310232152	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o	a82dd81ad4f4729f
423	15643	7743616321253850	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	3fde15b48472db6e
52	15662	7743616322037575	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	6e43e7ab505778b8
137	15710	7743616322567559	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	8c011da46c9e46c8
87	17441	7743616341813045	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	d771b10bfea04442
126	17664	7743616342498436	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	bc2f81c222f20cb5
323	19753	7743616362694080	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	1514f1b8c956646c
149	21328	7743616374923453	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	58e1a24f65980271
11578	21383	7743616378891396	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	d462f1f8163c023f
105	21695	7743616384289143	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	5f27a579dfb1063b
472	24108	7743616408374173	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	37db7daa03e3f8de
411	25645	7743616420424439	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	f7b3349896cb1db0
12177	25680	7743616423314198	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	83bf19d15d8e10e9
7400	26158	7743616423864317	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	3a13375e981c4d85
10890	27879	7743616440819805	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	2933875eb2123c93
14501	29388	7743616459669573	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	48bf29f8b8c4386b
24109	29950	7743616464599553	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	7d7b23e69585de29
17665	32059	7743616483776140	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	9d2fcc69ca55795e
15663	32079	7743616484076430	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	11829e6afeb6b2b0
21334	33410	7743616498720230	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	4ac7588f6ccb290f
7070	34437	7743616511357459	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	d53ea798664598c1
243	35127	7743616518027747	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	3448f781b36243f7
21696	35154	7743616518616100	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	3057b35b1bbc5b69
12859	35984	7743616524227546	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	d7d99deb0e1a9803
25655	36700	7743616532200990	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o	e91325d00a9463b2
25681	36703	7743616532321059	CMakeFiles/expo-modules-core.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	4ba6f53e7ad55be9
255	37686	7743616542220868	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	6005fedf91ce16a4
8188	38109	7743616548440850	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	e1b7b666fccb1442
21384	40794	7743616573991247	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	1c077034251c003b
26171	40871	7743616575300881	src/fabric/CMakeFiles/fabric.dir/FabricComponentsRegistry.cpp.o	e7b03f0f1bfef004
19762	41503	7743616582328073	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	19afe14b32e6eb1b
15645	41729	7743616584680761	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	60789170fe5f4564
15711	42635	7743616593851960	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	1a00aa888e03c378
17441	42738	7743616594801758	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	3e0f14d3da564c68
29951	43516	7743616602931623	src/fabric/CMakeFiles/fabric.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewEventEmitter.cpp.o	f8e8374b053f683
29392	45254	7743616620116701	src/fabric/CMakeFiles/fabric.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewShadowNode.cpp.o	b097692d76f91866
32059	46771	7743616635272250	src/fabric/CMakeFiles/fabric.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp.o	f6b7f8f2e493195d
27879	47787	7743616645481945	src/fabric/CMakeFiles/fabric.dir/K_/2025/thenextdoor/app/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewComponentDescriptor.cpp.o	50ba68f2b10ad1ce
47788	47942	7743616647052056	src/fabric/libfabric.a	7daa33358624305d
47942	48275	7743616650051916	../../../../build/intermediates/cxx/Debug/3s5c6d1f/obj/armeabi-v7a/libexpo-modules-core.so	6b12f545ced6da78
