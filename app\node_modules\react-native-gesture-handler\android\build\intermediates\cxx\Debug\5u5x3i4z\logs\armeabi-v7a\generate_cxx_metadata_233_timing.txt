# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 47ms
  generate-prefab-packages
    [gap of 36ms]
    exec-prefab 1035ms
    [gap of 27ms]
  generate-prefab-packages completed in 1098ms
  execute-generate-process
    exec-configure 418ms
    [gap of 97ms]
  execute-generate-process completed in 516ms
  [gap of 64ms]
generate_cxx_metadata completed in 1748ms

