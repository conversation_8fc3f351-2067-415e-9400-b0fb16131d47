<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="colorPrimary">#5BC4B3</color>
    <color name="colorPrimaryDark">#36798A</color>
    <color name="iconBackground">#FAFAFA</color>
    <color name="splashscreen_background">#FAFAFA</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">UNextDoor</string>
    <string name="expo_runtime_version">1.0.2</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">light</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
    <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style>
</resources>