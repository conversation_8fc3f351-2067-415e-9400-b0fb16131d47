[{"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\Exceptions.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\Exceptions.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\Exceptions.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\ExpoModulesHostObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\ExpoModulesHostObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\ExpoModulesHostObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIDeallocator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIDeallocator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIDeallocator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIFunctionBody.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIFunctionBody.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIFunctionBody.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIInjector.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIInjector.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIInjector.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIUtils.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIUtils.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIUtils.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSIContext.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSIContext.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSIContext.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSReferencesCache.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSReferencesCache.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSReferencesCache.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSharedObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSharedObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSharedObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaCallback.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaCallback.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaCallback.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaReferencesCache.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaReferencesCache.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaReferencesCache.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptFunction.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptFunction.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptFunction.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptModuleObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptModuleObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptModuleObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptRuntime.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptRuntime.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptRuntime.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptTypedArray.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptTypedArray.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptTypedArray.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptValue.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptValue.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptValue.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptWeakObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptWeakObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptWeakObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\MethodMetadata.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\MethodMetadata.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\MethodMetadata.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\RuntimeHolder.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\RuntimeHolder.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\RuntimeHolder.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\WeakRuntimeHolder.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\WeakRuntimeHolder.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\WeakRuntimeHolder.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\AnyType.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\AnyType.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\AnyType.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\ExpectedType.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\ExpectedType.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\ExpectedType.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\FrontendConverter.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverter.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverter.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react -IK:/2025/thenextdoor/app/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/thenextdoor/app/node_modules/react-native/ReactCommon -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/thenextdoor/app/node_modules/react-native/ReactCommon -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/thenextdoor/app/node_modules/react-native/ReactCommon -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/thenextdoor/app/node_modules/react-native/ReactCommon -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\K_\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp"}, {"directory": "K:/2025/thenextdoor/app/node_modules/expo-modules-core/android/.cxx/Debug/3s5c6d1f/armeabi-v7a", "command": "C:\\Android\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Android/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IK:/2025/thenextdoor/app/node_modules/react-native/ReactCommon -IK:/2025/thenextdoor/app/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=79 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\FabricComponentsRegistry.cpp.o -c K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\fabric\\FabricComponentsRegistry.cpp", "file": "K:\\2025\\thenextdoor\\app\\node_modules\\expo-modules-core\\android\\src\\fabric\\FabricComponentsRegistry.cpp"}]